#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MACD背离信号分析器
"""

import pandas as pd
import numpy as np
import os
import datetime
import glob
import logging
import sys
import base64
import io
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from send_mail import send_mail

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kline_analyzer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class KLineAnalyzer:
    def __init__(self, data_dir=None, target_date=None, kline_period="30min"):
        """
        K线分析器初始化

        参数:
        data_dir: 数据目录路径
        target_date: 目标日期，格式为YYYYMMDD，默认为当日
        kline_period: K线周期，默认30分钟
        """
        if data_dir is None:
            self.data_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
        else:
            self.data_dir = data_dir

        # 如果没有指定日期，使用当日日期
        if target_date is None:
            self.target_date = datetime.datetime.now().strftime('%Y%m%d')
        else:
            self.target_date = target_date

        self.target_path = os.path.join(self.data_dir, self.target_date)

        # K线周期配置
        self.kline_period = kline_period

        # ATR配置
        self.atr_period = 14  # ATR周期
        self.atr_multiplier = 2.0  # ATR倍数阈值
        self.price_change_threshold = 0.5  # 涨跌幅阈值（百分比）

        # BOLL配置
        self.boll_threshold = 0.95  # BOLL突破阈值（价格接近上下轨的比例）

        # K线图配置
        self.chart_history_bars = 50  # K线图显示的历史K线数量

        logging.info(f"K线分析器初始化完成")
        logging.info(f"数据目录: {self.target_path}")
        logging.info(f"目标日期: {self.target_date}")
        logging.info(f"K线周期: {self.kline_period}")
        logging.info(f"K线图历史数量: {self.chart_history_bars} 根")
        logging.info(f"ATR周期: {self.atr_period}, ATR倍数: {self.atr_multiplier}")
        logging.info(f"BOLL突破阈值: {self.boll_threshold}")
        logging.info(f"涨跌幅阈值: {self.price_change_threshold}% (已禁用)")
        logging.info("触发条件: MACD背离信号检测")

    def calculate_macd(self, data, fast_period=12, slow_period=26, signal_period=9):
        """
        计算MACD指标

        参数:
        data: DataFrame，包含收盘价
        fast_period: 快线周期，默认12
        slow_period: 慢线周期，默认26
        signal_period: 信号线周期，默认9

        返回:
        tuple: (macd_line, signal_line, macd_histogram)
        """
        close_prices = data['close']

        # 计算EMA
        ema_fast = close_prices.ewm(span=fast_period).mean()
        ema_slow = close_prices.ewm(span=slow_period).mean()

        # 计算MACD线
        macd_line = ema_fast - ema_slow

        # 计算信号线
        signal_line = macd_line.ewm(span=signal_period).mean()

        # 计算MACD柱
        macd_histogram = macd_line - signal_line

        return macd_line, signal_line, macd_histogram

    def calculate_atr(self, data, period=14):
        """
        计算ATR指标

        参数:
        data: DataFrame，包含高低收盘价
        period: ATR周期，默认14

        返回:
        Series: ATR值
        """
        high = data['high']
        low = data['low']
        close = data['close']

        # 计算真实波幅
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))

        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)

        # 计算ATR
        atr = tr.rolling(window=period).mean()

        return atr

    def find_entry_point(self, data_copy, start_idx):
        """
        寻找MACD柱绝对值减小的入场点

        参数:
        data_copy: 包含MACD数据的DataFrame
        start_idx: 开始搜索的索引位置（负数，从末尾开始）

        返回:
        int: 入场点的索引，如果没有找到返回None
        """
        # 从start_idx开始向后搜索（向最新数据方向）
        for i in range(start_idx, min(start_idx + 10, -1)):  # 最多搜索10根K线
            current_macd = data_copy['macd_histogram'].iloc[i]

            # 确保MACD柱小于0
            if current_macd >= 0:
                continue

            # 检查是否有前一根K线进行比较
            if i > start_idx:
                prev_macd = data_copy['macd_histogram'].iloc[i-1]

                # 检查MACD柱绝对值是否在减小
                if abs(current_macd) < abs(prev_macd):
                    return i

        return None

    def create_trade_signal(self, data_copy, entry_idx):
        """
        创建交易信号

        参数:
        data_copy: 包含数据的DataFrame
        entry_idx: 入场点索引

        返回:
        dict: 交易信号信息
        """
        if entry_idx is None or abs(entry_idx) >= len(data_copy):
            return None

        entry_row = data_copy.iloc[entry_idx]

        signal = {
            'entry_index': entry_idx,
            'entry_time': entry_row.get('datetime', 'Unknown'),
            'entry_price': entry_row['close'],
            'macd_histogram': entry_row['macd_histogram'],
            'signal_type': 'MACD背离入场'
        }

        return signal

    def find_macd_divergence_signals(self, data):
        """
        识别MACD背离信号和交易条件
        返回交易信号DataFrame
        """
        # 计算技术指标
        macd_line, signal_line, macd_histogram = self.calculate_macd(data)
        atr = self.calculate_atr(data)

        # 添加指标到数据中
        data_copy = data.copy()
        data_copy['macd_line'] = macd_line
        data_copy['signal_line'] = signal_line
        data_copy['macd_histogram'] = macd_histogram
        data_copy['atr'] = atr

        print("macd_histogram")
        print(macd_histogram.tail(10))  # 只打印最后10个值

        signals = []
        first_peak_found = False  # 标记是否已找到第一个峰值

        # 寻找MACD在零线下方的波峰（从最新数据开始检索）
        # 确保索引范围：i从3到len-3，这样-i的范围是-(len-3)到-3
        for i in range(3, len(data_copy) - 3):
            # 确保MACD柱在零线下方
            if data_copy['macd_histogram'].iloc[-i] >= 0:
                # 如果还没找到第一个峰值，但出现了MACD柱大于0，则不符合要求
                if not first_peak_found:
                    print(f"在第一个峰值前发现MACD柱大于0，索引={len(data_copy)-i}，跳过")
                continue

            # 检查是否为波峰（局部最小值），考虑前后两点
            if (data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-1] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+1] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i-2] and
                data_copy['macd_histogram'].iloc[-i] < data_copy['macd_histogram'].iloc[-i+2]):

                if not first_peak_found:
                    # 找到第一个峰值
                    first_peak_found = True
                    print(f"检查第一个波峰，i={i}, 实际索引={len(data_copy)-i}")

                    # 寻找前一个波峰（更早的数据）
                    prev_peak_idx = None
                    # j的范围：从i+2到min(i+50, len-3)，确保-j不会超出边界
                    for j in range(i + 2, min(i + 50, len(data_copy) - 3)):
                        if (data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-1] and
                            data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+1] and
                            data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j-2] and
                            data_copy['macd_histogram'].iloc[-j] < data_copy['macd_histogram'].iloc[-j+2] and
                            data_copy['macd_histogram'].iloc[-j] < 0):
                            prev_peak_idx = j
                            break

                    if prev_peak_idx is not None:
                        print(f"检查第二个波峰，j={prev_peak_idx}, 实际索引={len(data_copy)-prev_peak_idx}")
                        # 检查背离条件
                        current_macd = data_copy['macd_histogram'].iloc[-i]
                        prev_macd = data_copy['macd_histogram'].iloc[-prev_peak_idx]
                        current_price = data_copy['low'].iloc[-i]
                        prev_price = data_copy['low'].iloc[-prev_peak_idx]

                        # 信号1：MACD波峰抬升，价格降低（背离）
                        if current_macd > prev_macd and current_price < prev_price:
                            print("MACD背离")
                            # 寻找MACD柱绝对值减小的入场点
                            entry_idx = self.find_entry_point(data_copy, -i)

                            if entry_idx is not None:
                                entry_signal = self.create_trade_signal(data_copy, entry_idx)
                                if entry_signal:
                                    signals.append(entry_signal)
                        else:
                            print("未满足背离条件")

                    # 无论是否满足背离要求，找到第一个峰值后就停止继续检测
                    print("已检测第一个峰值，停止继续循环")
                    break
                else:
                    # 如果已经找到第一个峰值，不再继续检测
                    print(f"已找到第一个峰值，跳过后续峰值检测，i={i}")
                    break

        return pd.DataFrame(signals)

    def analyze_second_last_kline(self, df, symbol):
        """
        分析MACD背离信号并检查入场条件

        参数:
        df: K线数据DataFrame
        symbol: 品种代码

        返回:
        list: 异常K线信息列表
        """
        try:
            min_required_bars = max(self.atr_period + 1, self.chart_history_bars, 50)  # MACD需要更多数据
            if len(df) < min_required_bars:
                logging.warning(f"{symbol}: 数据不足，无法分析（需要至少{min_required_bars}根K线，当前{len(df)}根）")
                return []

            # 检查必要的列是否存在
            required_columns = ['open', 'high', 'low', 'close']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logging.warning(f"{symbol}: 缺少必要列: {missing_columns}")
                return []

            # 使用MACD背离分析
            signals_df = self.find_macd_divergence_signals(df)

            if signals_df.empty:
                logging.info(f"{symbol}: 未发现MACD背离信号")
                return []

            # 处理检测到的信号
            abnormal_klines = []

            for _, signal in signals_df.iterrows():
                # 获取入场点的K线数据
                entry_idx = signal['entry_index']
                if abs(entry_idx) >= len(df):
                    continue

                entry_row = df.iloc[entry_idx]

                # 计算基本指标
                price_change = ((entry_row['close'] - entry_row['open']) / entry_row['open']) * 100

                # 计算MACD相关信息
                macd_line, signal_line, macd_histogram = self.calculate_macd(df)
                current_macd_hist = macd_histogram.iloc[entry_idx]

                kline_info = {
                    'symbol': symbol,
                    'datetime': entry_row.get('datetime', f'Entry Point {entry_idx}'),
                    'open': entry_row['open'],
                    'high': entry_row['high'],
                    'low': entry_row['low'],
                    'close': entry_row['close'],
                    'volume': entry_row.get('volume', 0),
                    'price_change': price_change,
                    'change_type': '上涨' if price_change > 0 else '下跌',
                    'macd_histogram': current_macd_hist,
                    'entry_index': entry_idx,
                    'signal_type': signal['signal_type'],
                    'trigger_reason': [f"MACD背离信号，入场点MACD柱: {current_macd_hist:.4f}"],
                    'kline_data': df.copy()  # 保存完整的K线数据用于绘图
                }

                # 添加MACD指标到K线数据中
                kline_info['kline_data']['macd_line'] = macd_line
                kline_info['kline_data']['signal_line'] = signal_line
                kline_info['kline_data']['macd_histogram'] = macd_histogram

                abnormal_klines.append(kline_info)

                logging.info(f"{symbol}: 检测到MACD背离信号 - {kline_info['change_type']} {price_change:+.2f}%, MACD柱: {current_macd_hist:.4f}")
                logging.info(f"{symbol}: 入场点索引: {entry_idx}, 信号类型: {signal['signal_type']}")

            return abnormal_klines

        except Exception as e:
            logging.error(f"分析{symbol}MACD背离信号时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return []

    def create_kline_chart(self, df, symbol, abnormal_index=-2):
        """
        创建K线图，包含BOLL带、MACD柱和异常K线标记

        参数:
        df: K线数据DataFrame
        symbol: 品种代码
        abnormal_index: 异常K线的索引位置（默认-2，倒数第二根）

        返回:
        str: base64编码的图片数据
        """
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建子图：上方K线图，下方MACD图
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), height_ratios=[3, 1])

            # 显示最近50根K线（或全部数据如果不足50根）
            display_count = min(self.chart_history_bars, len(df))
            display_df = df.tail(display_count).copy()
            display_df.reset_index(drop=True, inplace=True)

            # 计算异常K线在显示数据中的位置
            if len(df) >= display_count:
                abnormal_display_index = len(display_df) + abnormal_index  # -2变成对应位置
            else:
                abnormal_display_index = len(df) + abnormal_index

            # 绘制K线（在上方子图ax1）
            for i, row in display_df.iterrows():
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # K线颜色
                if close_price >= open_price:
                    color = 'red'  # 阳线
                    body_bottom = open_price
                    body_height = close_price - open_price
                else:
                    color = 'green'  # 阴线
                    body_bottom = close_price
                    body_height = open_price - close_price

                # 绘制影线
                ax1.plot([i, i], [low_price, high_price], color='black', linewidth=1)

                # 绘制K线实体
                rect = Rectangle((i-0.3, body_bottom), 0.6, body_height,
                               facecolor=color, edgecolor='black', linewidth=1)
                ax1.add_patch(rect)

                # 标记异常K线
                if i == abnormal_display_index:
                    # 添加黄色边框标记异常K线
                    highlight_rect = Rectangle((i-0.4, low_price-0.5), 0.8, high_price-low_price+1,
                                             facecolor='none', edgecolor='yellow', linewidth=3)
                    ax1.add_patch(highlight_rect)

                    # 添加文字标记
                    ax1.annotate('异常K线', xy=(i, high_price), xytext=(i, high_price+2),
                              ha='center', va='bottom', fontsize=10, color='red',
                              arrowprops=dict(arrowstyle='->', color='red'))

            # 绘制BOLL带（在上方子图ax1）
            if all(col in display_df.columns for col in ['boll_upper', 'boll_middle', 'boll_lower']):
                x_values = range(len(display_df))
                ax1.plot(x_values, display_df['boll_upper'], 'b--', label='BOLL上轨', alpha=0.7)
                ax1.plot(x_values, display_df['boll_middle'], 'b-', label='BOLL中轨', alpha=0.7)
                ax1.plot(x_values, display_df['boll_lower'], 'b--', label='BOLL下轨', alpha=0.7)

                # 填充BOLL带区域
                ax1.fill_between(x_values, display_df['boll_upper'], display_df['boll_lower'],
                              alpha=0.1, color='blue')

            # 绘制MACD柱（在下方子图ax2）
            if 'macd_histogram' in display_df.columns:
                x_values = range(len(display_df))
                macd_hist = display_df['macd_histogram']

                # 绘制MACD柱状图
                colors = ['red' if x >= 0 else 'green' for x in macd_hist]
                ax2.bar(x_values, macd_hist, color=colors, alpha=0.7, width=0.8)

                # 绘制零线
                ax2.axhline(y=0, color='black', linestyle='-', linewidth=1)

                # 标记异常K线对应的MACD柱
                if abnormal_display_index < len(display_df):
                    ax2.bar(abnormal_display_index, macd_hist.iloc[abnormal_display_index],
                           color='yellow', alpha=0.9, width=0.8, edgecolor='orange', linewidth=2)

            # 设置上方K线图属性
            ax1.set_title(f'{symbol} K线图（{self.kline_period}周期，最近{display_count}根，标记异常K线）', fontsize=14, fontweight='bold')
            ax1.set_ylabel('价格', fontsize=12)
            ax1.grid(True, alpha=0.3)
            ax1.legend()

            # 设置下方MACD图属性
            ax2.set_title('MACD柱状图', fontsize=12)
            ax2.set_xlabel('K线序号', fontsize=12)
            ax2.set_ylabel('MACD柱', fontsize=10)
            ax2.grid(True, alpha=0.3)

            # 设置x轴标签，显示更多刻度
            tick_interval = max(1, len(display_df)//10)
            ax1.set_xticks(range(0, len(display_df), tick_interval))
            ax2.set_xticks(range(0, len(display_df), tick_interval))

            # 如果有时间信息，在K线图上方显示时间
            if 'datetime' in display_df.columns:
                try:
                    # 选择几个时间点显示
                    time_indices = range(0, len(display_df), max(1, len(display_df)//5))
                    time_labels = []
                    for i in time_indices:
                        if i < len(display_df):
                            dt = display_df.iloc[i]['datetime']
                            if pd.notna(dt):
                                time_labels.append(pd.to_datetime(dt).strftime('%H:%M'))
                            else:
                                time_labels.append('')

                    if time_labels:
                        ax1_top = ax1.twiny()
                        ax1_top.set_xlim(ax1.get_xlim())
                        ax1_top.set_xticks([i for i in time_indices if i < len(display_df)])
                        ax1_top.set_xticklabels(time_labels, fontsize=10)
                        ax1_top.set_xlabel('时间', fontsize=12)
                except:
                    pass  # 如果时间处理失败，忽略

            # 调整布局
            plt.tight_layout()

            # 保存图片到内存
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=100, bbox_inches='tight')
            img_buffer.seek(0)

            # 转换为base64
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')

            # 清理
            plt.close(fig)
            img_buffer.close()

            return img_base64

        except Exception as e:
            logging.error(f"创建{symbol}K线图时发生错误: {e}")
            plt.close('all')  # 确保清理所有图形
            return None

    def read_kline_files(self):
        """
        读取目标目录下的所有K线文件

        返回:
        dict: {symbol: DataFrame} 格式的数据字典
        """
        kline_data = {}

        if not os.path.exists(self.target_path):
            logging.error(f"目标目录不存在: {self.target_path}")
            return kline_data

        # 查找所有Excel文件
        excel_files = glob.glob(os.path.join(self.target_path, "*.xlsx"))

        if not excel_files:
            logging.warning(f"目录中没有找到Excel文件: {self.target_path}")
            return kline_data

        logging.info(f"找到 {len(excel_files)} 个Excel文件")

        for file_path in excel_files:
            try:
                # 从文件名提取品种代码
                file_name = os.path.basename(file_path)
                if file_name.startswith('~$'):  # 跳过临时文件
                    continue

                # 提取品种代码 (例如: DCE_v2509_kline_indicators.xlsx -> DCE.v2509)
                symbol = file_name.replace('_kline_indicators.xlsx', '').replace('_kline_data.xlsx', '').replace('_', '.')

                logging.info(f"正在读取文件: {file_name}")

                # 读取Excel文件
                df = pd.read_excel(file_path)

                if df.empty:
                    logging.warning(f"{symbol}: 文件为空")
                    continue

                # 检查必要的列
                required_columns = ['open', 'high', 'low', 'close']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    logging.warning(f"{symbol}: 缺少必要列: {missing_columns}")
                    continue

                # 数据清洗
                df = df.dropna(subset=required_columns)

                if len(df) == 0:
                    logging.warning(f"{symbol}: 清洗后数据为空")
                    continue

                # 检查是否包含指标列
                indicator_columns = ['atr', 'boll_upper', 'boll_middle', 'boll_lower']
                has_indicators = all(col in df.columns for col in indicator_columns)

                if has_indicators:
                    logging.info(f"{symbol}: 包含完整指标数据")
                else:
                    missing_indicators = [col for col in indicator_columns if col not in df.columns]
                    logging.warning(f"{symbol}: 缺少指标列: {missing_indicators}")
                    # 如果缺少指标，跳过该文件
                    continue

                kline_data[symbol] = df
                logging.info(f"{symbol}: 成功读取 {len(df)} 条K线数据")

            except Exception as e:
                logging.error(f"读取文件 {file_path} 时发生错误: {e}")
                continue

        logging.info(f"总共成功读取 {len(kline_data)} 个品种的数据")
        return kline_data

    def build_email_content(self, abnormal_klines):
        """
        构建邮件内容（包含K线图）

        参数:
        abnormal_klines: 异常K线列表

        返回:
        str: HTML格式的邮件内容
        """
        if not abnormal_klines:
            return ""

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        html_content = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 15px; text-align: center; margin-bottom: 20px; }}
                .content {{ padding: 0; }}
                .highlight-up {{ color: green; font-weight: bold; }}
                .highlight-down {{ color: red; font-weight: bold; }}
                .summary {{ background-color: #e8f4fd; padding: 15px; margin-bottom: 20px; border-radius: 5px; }}
                .symbol-section {{ margin-bottom: 40px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden; }}
                .symbol-header {{ background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }}
                .symbol-content {{ padding: 20px; }}
                .kline-info {{ display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px; }}
                .info-item {{ flex: 1; min-width: 200px; }}
                .info-label {{ font-weight: bold; color: #666; }}
                .info-value {{ margin-top: 5px; }}
                .chart-container {{ text-align: center; margin: 20px 0; }}
                .chart-image {{ max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 5px; }}
                .trigger-reasons {{ background-color: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>MACD背离信号提醒（30分钟K线）</h2>
            </div>
            <div class="content">
                <div class="summary">
                    <p><strong>检测到 {len(abnormal_klines)} 个品种的MACD背离信号！</strong></p>
                    <p><strong>分析时间：</strong>{current_time}</p>
                    <p><strong>数据日期：</strong>{self.target_date}</p>
                    <p><strong>K线周期：</strong>{self.kline_period}</p>
                    <p><strong>分析方法：</strong>MACD背离检测 + 入场点确认</p>
                    <p><strong>触发条件：</strong>MACD柱小于0且绝对值减小</p>
                </div>
        """

        # 为每个异常品种创建独立的展示区域
        for i, kline in enumerate(abnormal_klines, 1):
            highlight_class = "highlight-up" if kline['change_type'] == "上涨" else "highlight-down"

            # 格式化时间显示
            if kline['datetime'] != 'Second Last' and kline['datetime'] is not None:
                try:
                    if isinstance(kline['datetime'], str):
                        datetime_obj = pd.to_datetime(kline['datetime'])
                    else:
                        datetime_obj = kline['datetime']
                    datetime_str = datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
                    time_str = datetime_obj.strftime('%H:%M:%S')
                except:
                    datetime_str = str(kline['datetime'])
                    time_str = "未知时间"
            else:
                datetime_str = "倒数第二根K线"
                time_str = "倒数第二根"

            trigger_reasons = "; ".join(kline['trigger_reason'])

            # 生成K线图
            chart_base64 = None
            if 'kline_data' in kline and kline['kline_data'] is not None:
                chart_base64 = self.create_kline_chart(kline['kline_data'], kline['symbol'])

            html_content += f"""
                <div class="symbol-section">
                    <div class="symbol-header">
                        <h3>#{i} {kline['symbol']} - <span class="{highlight_class}">{kline['change_type']} {kline['price_change']:+.2f}%</span></h3>
                    </div>
                    <div class="symbol-content">
                        <div class="kline-info">
                            <div class="info-item">
                                <div class="info-label">开盘时间</div>
                                <div class="info-value">{datetime_str}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">时间点</div>
                                <div class="info-value">{time_str}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">开盘价</div>
                                <div class="info-value">{kline['open']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">收盘价</div>
                                <div class="info-value">{kline['close']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最高价</div>
                                <div class="info-value">{kline['high']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最低价</div>
                                <div class="info-value">{kline['low']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">MACD柱值</div>
                                <div class="info-value">{kline.get('macd_histogram', 'N/A'):.4f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">入场点索引</div>
                                <div class="info-value">{kline.get('entry_index', 'N/A')}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">信号类型</div>
                                <div class="info-value">{kline.get('signal_type', 'MACD背离')}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">成交量</div>
                                <div class="info-value">{kline.get('volume', 0):,}</div>
                            </div>
                        </div>

                        <div class="trigger-reasons">
                            <strong>触发原因：</strong>{trigger_reasons}
                        </div>
            """

            # 添加K线图
            if chart_base64:
                html_content += f"""
                        <div class="chart-container">
                            <h4>K线图+MACD柱（{self.kline_period}周期，最近{self.chart_history_bars}根，黄色标记为入场点）</h4>
                            <img src="data:image/png;base64,{chart_base64}" class="chart-image" alt="{kline['symbol']} K线图+MACD">
                        </div>
                """
            else:
                html_content += """
                        <div class="chart-container">
                            <p style="color: #666; font-style: italic;">K线图生成失败</p>
                        </div>
                """

            html_content += """
                    </div>
                </div>
            """

        html_content += f"""
                <div class="summary">
                    <h3>MACD背离分析参数说明</h3>
                    <p><strong>K线周期：</strong>{self.kline_period}</p>
                    <p><strong>历史K线数量：</strong>{self.chart_history_bars} 根</p>
                    <p><strong>MACD参数：</strong>快线12，慢线26，信号线9</p>
                    <p><strong>背离检测：</strong>MACD波峰抬升 + 价格下降</p>
                    <p><strong>入场条件：</strong>MACD柱 < 0 且绝对值减小</p>
                    <p><strong>图表说明：</strong>上方为K线图，下方为MACD柱状图，黄色标记为入场点</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_content

    def send_alert_email(self, abnormal_klines):
        """
        发送异常K线提醒邮件

        参数:
        abnormal_klines: 异常K线列表

        返回:
        bool: 是否发送成功
        """
        if not abnormal_klines:
            logging.info("没有异常K线，不发送邮件")
            return True

        try:
            # 构建邮件内容
            email_content = self.build_email_content(abnormal_klines)

            # 构建邮件主题
            up_count = sum(1 for k in abnormal_klines if k['change_type'] == '上涨')
            down_count = sum(1 for k in abnormal_klines if k['change_type'] == '下跌')
            macd_count = sum(1 for k in abnormal_klines if any('MACD' in reason for reason in k['trigger_reason']))

            subject = f"MACD背离信号提醒({self.kline_period}) - {self.target_date} - {len(abnormal_klines)}个品种(上涨{up_count}个,下跌{down_count}个,MACD背离{macd_count}个)"

            # 发送邮件
            send_mail(
                mail_content=email_content,
                subject=subject,
                receives=['<EMAIL>']  # 可以根据需要修改接收邮箱
            )

            logging.info(f"异常K线提醒邮件发送成功，共 {len(abnormal_klines)} 个异常K线")
            return True

        except Exception as e:
            logging.error(f"发送邮件时发生错误: {e}")
            return False

    def analyze_all_symbols(self):
        """
        分析所有品种的K线数据

        返回:
        list: 所有异常K线的列表
        """
        logging.info("开始分析所有品种的K线数据...")

        # 读取K线数据
        kline_data = self.read_kline_files()

        if not kline_data:
            logging.warning("没有读取到任何K线数据")
            return []

        all_abnormal_klines = []

        # 分析每个品种的倒数第二根K线
        for symbol, df in kline_data.items():
            logging.info(f"正在分析品种: {symbol} (倒数第二根K线)")

            abnormal_klines = self.analyze_second_last_kline(df, symbol)

            if abnormal_klines:
                all_abnormal_klines.extend(abnormal_klines)
                logging.info(f"{symbol}: 倒数第二根K线异常")
            else:
                logging.info(f"{symbol}: 倒数第二根K线正常")

        logging.info(f"分析完成，总共发现 {len(all_abnormal_klines)} 个异常K线")

        return all_abnormal_klines

    def run_analysis(self, send_email=True):
        """
        运行完整的分析流程

        参数:
        send_email: 是否发送邮件

        返回:
        dict: 分析结果
        """
        try:
            logging.info("=" * 50)
            logging.info("开始倒数第二根K线异常波动分析（ATR+BOLL指标）")
            logging.info("=" * 50)

            # 分析所有品种
            abnormal_klines = self.analyze_all_symbols()

            # 统计结果
            result = {
                'total_abnormal': len(abnormal_klines),
                'up_count': sum(1 for k in abnormal_klines if k['change_type'] == '上涨'),
                'down_count': sum(1 for k in abnormal_klines if k['change_type'] == '下跌'),
                'macd_divergence_count': sum(1 for k in abnormal_klines if any('MACD' in reason for reason in k['trigger_reason'])),
                'symbols': list(set(k['symbol'] for k in abnormal_klines)),
                'abnormal_klines': abnormal_klines
            }

            # 输出统计信息
            logging.info("=" * 30)
            logging.info("MACD背离信号分析结果统计")
            logging.info("=" * 30)
            logging.info(f"信号品种总数: {result['total_abnormal']}")
            logging.info(f"上涨信号: {result['up_count']} 个品种")
            logging.info(f"下跌信号: {result['down_count']} 个品种")
            logging.info(f"MACD背离: {result['macd_divergence_count']} 个品种")
            logging.info(f"涉及品种: {len(result['symbols'])} 个")
            logging.info(f"K线周期: {self.kline_period}")
            logging.info("分析方法: MACD背离检测 + 入场点确认")

            if result['symbols']:
                logging.info(f"异常品种列表: {', '.join(result['symbols'])}")

            # 发送邮件
            if send_email and abnormal_klines:
                email_sent = self.send_alert_email(abnormal_klines)
                result['email_sent'] = email_sent
            else:
                result['email_sent'] = False
                if not abnormal_klines:
                    logging.info("没有异常K线，不发送邮件")
                else:
                    logging.info("邮件发送已禁用")

            logging.info("=" * 50)
            logging.info("倒数第二根K线异常波动分析完成")
            logging.info("=" * 50)

            return result

        except Exception as e:
            logging.error(f"运行分析时发生错误: {e}")
            return {
                'total_abnormal': 0,
                'up_count': 0,
                'down_count': 0,
                'boll_breakthrough_count': 0,
                'atr_exceeded_count': 0,
                'price_change_count': 0,
                'symbols': [],
                'abnormal_klines': [],
                'email_sent': False,
                'error': str(e)
            }

def main():
    """
    主函数
    """
    print("=" * 60)
    print("MACD背离信号分析器（30分钟K线周期）")
    print("=" * 60)

    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "help" or command == "-h":
            print("使用方法:")
            print("  python b1.py                    - 分析当日MACD背离信号并发送邮件")
            print("  python b1.py [日期]             - 分析指定日期的MACD背离信号")
            print("  python b1.py [日期] --no-email  - 分析但不发送邮件")
            print("  python b1.py test               - 测试模式（不发送邮件）")
            print("  python b1.py help               - 显示帮助信息")
            print("")
            print("日期格式: YYYYMMDD (例如: 20250109)")
            print("默认使用当日日期")
            print("分析方法: MACD背离检测 + 入场点确认")
            print("K线周期: 30分钟")
            print("K线图历史: 50根K线")
            print("触发条件: MACD柱 < 0 且绝对值减小")
            print("图表内容: K线图 + MACD柱状图")
            return

        elif command == "test":
            print("测试模式：分析MACD背离信号但不发送邮件")
            analyzer = KLineAnalyzer()  # 使用当日日期
            result = analyzer.run_analysis(send_email=False)
            print(f"测试完成，发现 {result['total_abnormal']} 个品种的MACD背离信号")
            return

    # 解析参数
    target_date = None  # 默认使用当日
    send_email = True

    if len(sys.argv) > 1:
        # 检查是否是日期格式
        date_arg = sys.argv[1]
        if date_arg.isdigit() and len(date_arg) == 8:
            target_date = date_arg
            print(f"使用指定日期: {target_date}")

        # 检查是否禁用邮件
        if "--no-email" in sys.argv:
            send_email = False
            print("邮件发送已禁用")

    try:
        # 创建分析器
        analyzer = KLineAnalyzer(target_date=target_date)

        # 运行分析
        result = analyzer.run_analysis(send_email=send_email)

        # 输出最终结果
        print("\n" + "=" * 40)
        print("最终结果")
        print("=" * 40)
        print(f"分析日期: {analyzer.target_date}")
        print(f"K线周期: {analyzer.kline_period}")
        print(f"分析方法: MACD背离检测 + 入场点确认")
        print(f"K线图历史: {analyzer.chart_history_bars} 根")
        print(f"触发条件: MACD柱 < 0 且绝对值减小")
        print(f"信号品种总数: {result['total_abnormal']}")
        print(f"上涨信号: {result['up_count']} 个品种")
        print(f"下跌信号: {result['down_count']} 个品种")
        print(f"MACD背离: {result['macd_divergence_count']} 个品种")
        print(f"涉及品种数: {len(result['symbols'])}")

        if result.get('email_sent'):
            print("[成功] 邮件发送成功")
        elif result['total_abnormal'] > 0:
            print("[失败] 邮件发送失败或已禁用")
        else:
            print("[信息] 无需发送邮件")

        if 'error' in result:
            print(f"错误: {result['error']}")

        print("=" * 40)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logging.error(f"程序运行时发生错误: {e}")
        print(f"程序运行失败: {e}")

if __name__ == "__main__":
    main()