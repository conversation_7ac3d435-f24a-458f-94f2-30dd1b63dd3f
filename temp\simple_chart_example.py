#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的create_kline_chart调用示例
"""

import os
import pandas as pd
import base64
from b1_macd_1 import KLineAnalyzer

def simple_chart_example():
    """
    最简单的create_kline_chart调用示例
    """
    print("🚀 简化的create_kline_chart调用示例")
    print("=" * 50)
    
    # 步骤1: 创建分析器
    analyzer = KLineAnalyzer(data_dir="data", kline_period="30min")
    
    # 步骤2: 读取数据
    data_files = [f for f in os.listdir(analyzer.target_path) if f.endswith('.xlsx')]
    test_file = data_files[0]
    file_path = os.path.join(analyzer.target_path, test_file)
    df = pd.read_excel(file_path)
    symbol = test_file.replace('_kline_data.xlsx', '').replace('_', '.')
    
    print(f"📊 使用数据: {symbol} ({len(df)}根K线)")
    
    # 步骤3: 调用create_kline_chart函数
    print(f"\n🎯 调用create_kline_chart函数...")
    
    # 基本调用（只需要df和symbol）
    chart_base64 = analyzer.create_kline_chart(
        df=df,           # K线数据
        symbol=symbol    # 品种代码
    )
    
    if chart_base64:
        print(f"✅ 成功生成图表!")
        print(f"   📏 Base64长度: {len(chart_base64)} 字符")
        print(f"   💾 图片大小: {len(chart_base64) * 3 // 4 // 1024:.1f} KB")
        
        # 保存为图片文件
        save_as_png(chart_base64, "simple_example.png")
        
        # 保存为HTML文件
        save_as_html(chart_base64, symbol, "simple_example.html")
        
        print(f"   📁 已保存为: simple_example.png 和 simple_example.html")
    else:
        print(f"❌ 图表生成失败")

def save_as_png(chart_base64, filename):
    """将base64图片保存为PNG文件"""
    try:
        img_data = base64.b64decode(chart_base64)
        with open(filename, "wb") as f:
            f.write(img_data)
    except Exception as e:
        print(f"保存PNG失败: {e}")

def save_as_html(chart_base64, symbol, filename):
    """将图片保存为HTML文件"""
    html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{symbol} K线图</title>
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; margin: 20px; }}
        img {{ max-width: 100%; border: 1px solid #ddd; }}
    </style>
</head>
<body>
    <h1>{symbol} K线图表</h1>
    <img src="data:image/png;base64,{chart_base64}" alt="K线图">
</body>
</html>
"""
    with open(filename, "w", encoding="utf-8") as f:
        f.write(html)

if __name__ == "__main__":
    simple_chart_example()
