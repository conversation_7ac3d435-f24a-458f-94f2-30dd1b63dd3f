import pandas as pd
import numpy as np
import os
import datetime
import glob
import logging
import sys
from send_mail import send_mail

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kline_analyzer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class KLineAnalyzer:
    def __init__(self, data_dir=None, target_date=None):
        """
        K线分析器初始化

        参数:
        data_dir: 数据目录路径
        target_date: 目标日期，格式为YYYYMMDD，默认为当日
        """
        if data_dir is None:
            self.data_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
        else:
            self.data_dir = data_dir

        # 如果没有指定日期，使用当日日期
        if target_date is None:
            self.target_date = datetime.datetime.now().strftime('%Y%m%d')
        else:
            self.target_date = target_date

        self.target_path = os.path.join(self.data_dir, self.target_date)

        # ATR配置
        self.atr_period = 14  # ATR周期
        self.atr_multiplier = 2.0  # ATR倍数阈值
        self.price_change_threshold = 0.5  # 涨跌幅阈值（百分比）

        # BOLL配置
        self.boll_threshold = 0.95  # BOLL突破阈值（价格接近上下轨的比例）

        logging.info(f"K线分析器初始化完成")
        logging.info(f"数据目录: {self.target_path}")
        logging.info(f"目标日期: {self.target_date}")
        logging.info(f"ATR周期: {self.atr_period}, ATR倍数: {self.atr_multiplier}")
        logging.info(f"BOLL突破阈值: {self.boll_threshold}")
        logging.info(f"涨跌幅阈值: {self.price_change_threshold}% (已禁用)")
        logging.info("触发条件: 仅使用ATR和BOLL指标")

    def analyze_second_last_kline(self, df, symbol):
        """
        分析倒数第二根K线是否满足异常条件（使用已计算的ATR和BOLL指标）

        参数:
        df: K线数据DataFrame
        symbol: 品种代码

        返回:
        list: 异常K线信息列表（最多包含1个元素）
        """
        try:
            if len(df) < self.atr_period + 1:
                logging.warning(f"{symbol}: 数据不足，无法分析（需要至少{self.atr_period + 1}根K线）")
                return []

            # 检查必要的列是否存在
            required_columns = ['open', 'high', 'low', 'close', 'atr', 'boll_upper', 'boll_middle', 'boll_lower']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logging.warning(f"{symbol}: 缺少必要列: {missing_columns}")
                return []

            # 只分析倒数第二根K线
            second_last_row = df.iloc[-2]

            # 检查ATR值是否有效
            if pd.isna(second_last_row['atr']) or second_last_row['atr'] == 0:
                logging.warning(f"{symbol}: 倒数第二根K线的ATR值无效")
                return []

            # 计算倒数第二根K线的指标
            price_change = ((second_last_row['close'] - second_last_row['open']) / second_last_row['open']) * 100
            candle_body = abs(second_last_row['close'] - second_last_row['open'])
            body_atr_ratio = candle_body / second_last_row['atr']

            # 计算BOLL位置（避免除零错误）
            boll_range = second_last_row['boll_upper'] - second_last_row['boll_lower']
            if boll_range == 0:
                boll_position = 0.5  # 如果上下轨相等，设为中间位置
            else:
                boll_position = (second_last_row['close'] - second_last_row['boll_lower']) / boll_range

            # 判断是否满足异常条件
            # 条件1: 涨跌幅超过阈值 (已禁用)
            # price_change_exceeded = abs(price_change) >= self.price_change_threshold

            # 条件2: K线实体超过ATR倍数阈值
            atr_exceeded = body_atr_ratio >= self.atr_multiplier

            # 条件3: BOLL突破（接近上轨或下轨）
            boll_breakthrough = (boll_position >= self.boll_threshold or
                               boll_position <= (1 - self.boll_threshold))

            # 满足条件2或条件3即为异常K线（条件1已禁用）
            if atr_exceeded or boll_breakthrough:
                kline_info = {
                    'symbol': symbol,
                    'datetime': second_last_row.get('datetime', 'Second Last'),
                    'open': second_last_row['open'],
                    'high': second_last_row['high'],
                    'low': second_last_row['low'],
                    'close': second_last_row['close'],
                    'volume': second_last_row.get('volume', 0),
                    'price_change': price_change,
                    'candle_body': candle_body,
                    'atr': second_last_row['atr'],
                    'atr_tr': second_last_row.get('atr_tr', 0),
                    'body_atr_ratio': body_atr_ratio,
                    'boll_upper': second_last_row['boll_upper'],
                    'boll_middle': second_last_row['boll_middle'],
                    'boll_lower': second_last_row['boll_lower'],
                    'boll_position': boll_position,
                    'change_type': '上涨' if price_change > 0 else '下跌',
                    'trigger_reason': []
                }

                # 记录触发原因（只记录条件2和条件3）
                # 条件1已禁用，不再记录涨跌幅触发原因

                if atr_exceeded:
                    kline_info['trigger_reason'].append(f"K线实体是ATR的{body_atr_ratio:.2f}倍")

                if boll_breakthrough:
                    if boll_position >= self.boll_threshold:
                        kline_info['trigger_reason'].append(f"突破BOLL上轨(位置{boll_position:.2f})")
                    else:
                        kline_info['trigger_reason'].append(f"突破BOLL下轨(位置{boll_position:.2f})")

                logging.info(f"{symbol}: 倒数第二根K线异常 - {kline_info['change_type']} {price_change:+.2f}%, ATR比值: {body_atr_ratio:.2f}, BOLL位置: {boll_position:.2f}")
                logging.info(f"{symbol}: 触发原因: {'; '.join(kline_info['trigger_reason'])}")

                return [kline_info]
            else:
                logging.info(f"{symbol}: 倒数第二根K线正常 - {price_change:+.2f}%, ATR比值: {body_atr_ratio:.2f}, BOLL位置: {boll_position:.2f}")
                return []

        except Exception as e:
            logging.error(f"分析{symbol}倒数第二根K线时发生错误: {e}")
            return []

    def read_kline_files(self):
        """
        读取目标目录下的所有K线文件

        返回:
        dict: {symbol: DataFrame} 格式的数据字典
        """
        kline_data = {}

        if not os.path.exists(self.target_path):
            logging.error(f"目标目录不存在: {self.target_path}")
            return kline_data

        # 查找所有Excel文件
        excel_files = glob.glob(os.path.join(self.target_path, "*.xlsx"))

        if not excel_files:
            logging.warning(f"目录中没有找到Excel文件: {self.target_path}")
            return kline_data

        logging.info(f"找到 {len(excel_files)} 个Excel文件")

        for file_path in excel_files:
            try:
                # 从文件名提取品种代码
                file_name = os.path.basename(file_path)
                if file_name.startswith('~$'):  # 跳过临时文件
                    continue

                # 提取品种代码 (例如: DCE_v2509_kline_indicators.xlsx -> DCE.v2509)
                symbol = file_name.replace('_kline_indicators.xlsx', '').replace('_kline_data.xlsx', '').replace('_', '.')

                logging.info(f"正在读取文件: {file_name}")

                # 读取Excel文件
                df = pd.read_excel(file_path)

                if df.empty:
                    logging.warning(f"{symbol}: 文件为空")
                    continue

                # 检查必要的列
                required_columns = ['open', 'high', 'low', 'close']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    logging.warning(f"{symbol}: 缺少必要列: {missing_columns}")
                    continue

                # 数据清洗
                df = df.dropna(subset=required_columns)

                if len(df) == 0:
                    logging.warning(f"{symbol}: 清洗后数据为空")
                    continue

                # 检查是否包含指标列
                indicator_columns = ['atr', 'boll_upper', 'boll_middle', 'boll_lower']
                has_indicators = all(col in df.columns for col in indicator_columns)

                if has_indicators:
                    logging.info(f"{symbol}: 包含完整指标数据")
                else:
                    missing_indicators = [col for col in indicator_columns if col not in df.columns]
                    logging.warning(f"{symbol}: 缺少指标列: {missing_indicators}")
                    # 如果缺少指标，跳过该文件
                    continue

                kline_data[symbol] = df
                logging.info(f"{symbol}: 成功读取 {len(df)} 条K线数据")

            except Exception as e:
                logging.error(f"读取文件 {file_path} 时发生错误: {e}")
                continue

        logging.info(f"总共成功读取 {len(kline_data)} 个品种的数据")
        return kline_data

    def build_email_content(self, abnormal_klines):
        """
        构建邮件内容

        参数:
        abnormal_klines: 异常K线列表

        返回:
        str: HTML格式的邮件内容
        """
        if not abnormal_klines:
            return ""

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        html_content = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; }}
                .header {{ background-color: #f0f0f0; padding: 10px; text-align: center; }}
                .content {{ padding: 20px; }}
                .highlight-up {{ color: green; font-weight: bold; }}
                .highlight-down {{ color: red; font-weight: bold; }}
                .data-table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                .data-table th, .data-table td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                .data-table th {{ background-color: #f2f2f2; }}
                .summary {{ background-color: #e8f4fd; padding: 10px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>倒数第二根K线异常波动提醒（ATR+BOLL指标）</h2>
            </div>
            <div class="content">
                <div class="summary">
                    <p><strong>检测到 {len(abnormal_klines)} 个品种的倒数第二根K线异常！</strong></p>
                    <p><strong>分析时间：</strong>{current_time}</p>
                    <p><strong>数据日期：</strong>{self.target_date}</p>
                    <p><strong>分析范围：</strong>仅倒数第二根K线</p>
                </div>

                <table class="data-table">
                    <tr>
                        <th>品种代码</th>
                        <th>时间</th>
                        <th>涨跌类型</th>
                        <th>涨跌幅</th>
                        <th>开盘价</th>
                        <th>收盘价</th>
                        <th>ATR值</th>
                        <th>实体/ATR比值</th>
                        <th>BOLL位置</th>
                        <th>BOLL上轨</th>
                        <th>BOLL下轨</th>
                        <th>触发原因</th>
                    </tr>
        """

        for kline in abnormal_klines:
            highlight_class = "highlight-up" if kline['change_type'] == "上涨" else "highlight-down"
            datetime_str = str(kline['datetime']) if kline['datetime'] else "N/A"
            trigger_reasons = "; ".join(kline['trigger_reason'])

            html_content += f"""
                    <tr>
                        <td>{kline['symbol']}</td>
                        <td>{datetime_str}</td>
                        <td><span class="{highlight_class}">{kline['change_type']}</span></td>
                        <td><span class="{highlight_class}">{kline['price_change']:+.2f}%</span></td>
                        <td>{kline['open']:.2f}</td>
                        <td>{kline['close']:.2f}</td>
                        <td>{kline['atr']:.2f}</td>
                        <td>{kline['body_atr_ratio']:.2f}</td>
                        <td>{kline['boll_position']:.2f}</td>
                        <td>{kline['boll_upper']:.2f}</td>
                        <td>{kline['boll_lower']:.2f}</td>
                        <td>{trigger_reasons}</td>
                    </tr>
            """

        html_content += """
                </table>

                <div class="summary">
                    <h3>分析参数</h3>
                    <p><strong>ATR周期：</strong>{} 根K线</p>
                    <p><strong>ATR倍数阈值：</strong>{}</p>
                    <p><strong>涨跌幅阈值：</strong>{}%</p>
                    <p><strong>BOLL突破阈值：</strong>{}</p>
                </div>
            </div>
        </body>
        </html>
        """.format(self.atr_period, self.atr_multiplier, self.price_change_threshold, self.boll_threshold)

        return html_content

    def send_alert_email(self, abnormal_klines):
        """
        发送异常K线提醒邮件

        参数:
        abnormal_klines: 异常K线列表

        返回:
        bool: 是否发送成功
        """
        if not abnormal_klines:
            logging.info("没有异常K线，不发送邮件")
            return True

        try:
            # 构建邮件内容
            email_content = self.build_email_content(abnormal_klines)

            # 构建邮件主题
            up_count = sum(1 for k in abnormal_klines if k['change_type'] == '上涨')
            down_count = sum(1 for k in abnormal_klines if k['change_type'] == '下跌')
            boll_count = sum(1 for k in abnormal_klines if any('BOLL' in reason for reason in k['trigger_reason']))
            atr_count = sum(1 for k in abnormal_klines if any('ATR' in reason for reason in k['trigger_reason']))

            subject = f"倒数第二根K线异常提醒(ATR+BOLL) - {self.target_date} - {len(abnormal_klines)}个品种异常(上涨{up_count}个,下跌{down_count}个,ATR超标{atr_count}个,BOLL突破{boll_count}个)"

            # 发送邮件
            send_mail(
                mail_content=email_content,
                subject=subject,
                receives=['<EMAIL>']  # 可以根据需要修改接收邮箱
            )

            logging.info(f"异常K线提醒邮件发送成功，共 {len(abnormal_klines)} 个异常K线")
            return True

        except Exception as e:
            logging.error(f"发送邮件时发生错误: {e}")
            return False

    def analyze_all_symbols(self):
        """
        分析所有品种的K线数据

        返回:
        list: 所有异常K线的列表
        """
        logging.info("开始分析所有品种的K线数据...")

        # 读取K线数据
        kline_data = self.read_kline_files()

        if not kline_data:
            logging.warning("没有读取到任何K线数据")
            return []

        all_abnormal_klines = []

        # 分析每个品种的倒数第二根K线
        for symbol, df in kline_data.items():
            logging.info(f"正在分析品种: {symbol} (倒数第二根K线)")

            abnormal_klines = self.analyze_second_last_kline(df, symbol)

            if abnormal_klines:
                all_abnormal_klines.extend(abnormal_klines)
                logging.info(f"{symbol}: 倒数第二根K线异常")
            else:
                logging.info(f"{symbol}: 倒数第二根K线正常")

        logging.info(f"分析完成，总共发现 {len(all_abnormal_klines)} 个异常K线")

        return all_abnormal_klines

    def run_analysis(self, send_email=True):
        """
        运行完整的分析流程

        参数:
        send_email: 是否发送邮件

        返回:
        dict: 分析结果
        """
        try:
            logging.info("=" * 50)
            logging.info("开始倒数第二根K线异常波动分析（ATR+BOLL指标）")
            logging.info("=" * 50)

            # 分析所有品种
            abnormal_klines = self.analyze_all_symbols()

            # 统计结果
            result = {
                'total_abnormal': len(abnormal_klines),
                'up_count': sum(1 for k in abnormal_klines if k['change_type'] == '上涨'),
                'down_count': sum(1 for k in abnormal_klines if k['change_type'] == '下跌'),
                'boll_breakthrough_count': sum(1 for k in abnormal_klines if any('BOLL' in reason for reason in k['trigger_reason'])),
                'atr_exceeded_count': sum(1 for k in abnormal_klines if any('ATR' in reason for reason in k['trigger_reason'])),
                'price_change_count': sum(1 for k in abnormal_klines if any('涨跌幅' in reason for reason in k['trigger_reason'])),
                'symbols': list(set(k['symbol'] for k in abnormal_klines)),
                'abnormal_klines': abnormal_klines
            }

            # 输出统计信息
            logging.info("=" * 30)
            logging.info("倒数第二根K线分析结果统计（仅ATR+BOLL条件）")
            logging.info("=" * 30)
            logging.info(f"异常品种总数: {result['total_abnormal']}")
            logging.info(f"上涨异常: {result['up_count']} 个品种")
            logging.info(f"下跌异常: {result['down_count']} 个品种")
            logging.info(f"ATR超标: {result['atr_exceeded_count']} 个品种")
            logging.info(f"BOLL突破: {result['boll_breakthrough_count']} 个品种")
            logging.info(f"涉及品种: {len(result['symbols'])} 个")
            logging.info("注意: 涨跌幅条件已禁用")

            if result['symbols']:
                logging.info(f"异常品种列表: {', '.join(result['symbols'])}")

            # 发送邮件
            if send_email and abnormal_klines:
                email_sent = self.send_alert_email(abnormal_klines)
                result['email_sent'] = email_sent
            else:
                result['email_sent'] = False
                if not abnormal_klines:
                    logging.info("没有异常K线，不发送邮件")
                else:
                    logging.info("邮件发送已禁用")

            logging.info("=" * 50)
            logging.info("倒数第二根K线异常波动分析完成")
            logging.info("=" * 50)

            return result

        except Exception as e:
            logging.error(f"运行分析时发生错误: {e}")
            return {
                'total_abnormal': 0,
                'up_count': 0,
                'down_count': 0,
                'boll_breakthrough_count': 0,
                'atr_exceeded_count': 0,
                'price_change_count': 0,
                'symbols': [],
                'abnormal_klines': [],
                'email_sent': False,
                'error': str(e)
            }

def main():
    """
    主函数
    """
    print("=" * 60)
    print("倒数第二根K线异常波动分析器（仅ATR+BOLL指标）")
    print("=" * 60)

    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "help" or command == "-h":
            print("使用方法:")
            print("  python b1.py                    - 分析当日倒数第二根K线并发送邮件")
            print("  python b1.py [日期]             - 分析指定日期的倒数第二根K线")
            print("  python b1.py [日期] --no-email  - 分析但不发送邮件")
            print("  python b1.py test               - 测试模式（不发送邮件）")
            print("  python b1.py help               - 显示帮助信息")
            print("")
            print("日期格式: YYYYMMDD (例如: 20250109)")
            print("默认使用当日日期")
            print("注意: 只分析每个品种的倒数第二根K线")
            print("触发条件: 仅使用ATR和BOLL指标（涨跌幅条件已禁用）")
            return

        elif command == "test":
            print("测试模式：分析倒数第二根K线但不发送邮件")
            analyzer = KLineAnalyzer()  # 使用当日日期
            result = analyzer.run_analysis(send_email=False)
            print(f"测试完成，发现 {result['total_abnormal']} 个品种的倒数第二根K线异常")
            return

    # 解析参数
    target_date = None  # 默认使用当日
    send_email = True

    if len(sys.argv) > 1:
        # 检查是否是日期格式
        date_arg = sys.argv[1]
        if date_arg.isdigit() and len(date_arg) == 8:
            target_date = date_arg
            print(f"使用指定日期: {target_date}")

        # 检查是否禁用邮件
        if "--no-email" in sys.argv:
            send_email = False
            print("邮件发送已禁用")

    try:
        # 创建分析器
        analyzer = KLineAnalyzer(target_date=target_date)

        # 运行分析
        result = analyzer.run_analysis(send_email=send_email)

        # 输出最终结果
        print("\n" + "=" * 40)
        print("最终结果")
        print("=" * 40)
        print(f"分析日期: {analyzer.target_date}")
        print(f"分析范围: 仅倒数第二根K线")
        print(f"触发条件: 仅ATR+BOLL（涨跌幅条件已禁用）")
        print(f"异常品种总数: {result['total_abnormal']}")
        print(f"上涨异常: {result['up_count']} 个品种")
        print(f"下跌异常: {result['down_count']} 个品种")
        print(f"ATR超标: {result['atr_exceeded_count']} 个品种")
        print(f"BOLL突破: {result['boll_breakthrough_count']} 个品种")
        print(f"涉及品种数: {len(result['symbols'])}")

        if result.get('email_sent'):
            print("✓ 邮件发送成功")
        elif result['total_abnormal'] > 0:
            print("✗ 邮件发送失败或已禁用")
        else:
            print("- 无需发送邮件")

        if 'error' in result:
            print(f"错误: {result['error']}")

        print("=" * 40)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logging.error(f"程序运行时发生错误: {e}")
        print(f"程序运行失败: {e}")

if __name__ == "__main__":
    main()