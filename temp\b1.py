import pandas as pd
import numpy as np
import os
import datetime
import glob
import logging
import sys
import base64
import io
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.patches import Rectangle
from send_mail import send_mail

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('kline_analyzer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class KLineAnalyzer:
    def __init__(self, data_dir=None, target_date=None):
        """
        K线分析器初始化

        参数:
        data_dir: 数据目录路径
        target_date: 目标日期，格式为YYYYMMDD，默认为当日
        """
        if data_dir is None:
            self.data_dir = r"D:\edge下载\stock\k-xian\huizong\量化\天勤量化\tqsdk-python\temp\data"
        else:
            self.data_dir = data_dir

        # 如果没有指定日期，使用当日日期
        if target_date is None:
            self.target_date = datetime.datetime.now().strftime('%Y%m%d')
        else:
            self.target_date = target_date

        self.target_path = os.path.join(self.data_dir, self.target_date)

        # ATR配置
        self.atr_period = 14  # ATR周期
        self.atr_multiplier = 2.0  # ATR倍数阈值
        self.price_change_threshold = 0.5  # 涨跌幅阈值（百分比）

        # BOLL配置
        self.boll_threshold = 0.95  # BOLL突破阈值（价格接近上下轨的比例）

        logging.info(f"K线分析器初始化完成")
        logging.info(f"数据目录: {self.target_path}")
        logging.info(f"目标日期: {self.target_date}")
        logging.info(f"ATR周期: {self.atr_period}, ATR倍数: {self.atr_multiplier}")
        logging.info(f"BOLL突破阈值: {self.boll_threshold}")
        logging.info(f"涨跌幅阈值: {self.price_change_threshold}% (已禁用)")
        logging.info("触发条件: 仅使用ATR和BOLL指标")

    def analyze_second_last_kline(self, df, symbol):
        """
        分析倒数第二根K线是否满足异常条件（使用已计算的ATR和BOLL指标）

        参数:
        df: K线数据DataFrame
        symbol: 品种代码

        返回:
        list: 异常K线信息列表（最多包含1个元素）
        """
        try:
            if len(df) < self.atr_period + 1:
                logging.warning(f"{symbol}: 数据不足，无法分析（需要至少{self.atr_period + 1}根K线）")
                return []

            # 检查必要的列是否存在
            required_columns = ['open', 'high', 'low', 'close', 'atr', 'boll_upper', 'boll_middle', 'boll_lower']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                logging.warning(f"{symbol}: 缺少必要列: {missing_columns}")
                return []

            # 只分析倒数第二根K线
            second_last_row = df.iloc[-2]

            # 检查ATR值是否有效
            if pd.isna(second_last_row['atr']) or second_last_row['atr'] == 0:
                logging.warning(f"{symbol}: 倒数第二根K线的ATR值无效")
                return []

            # 计算倒数第二根K线的指标
            price_change = ((second_last_row['close'] - second_last_row['open']) / second_last_row['open']) * 100
            candle_body = abs(second_last_row['close'] - second_last_row['open'])
            body_atr_ratio = candle_body / second_last_row['atr']

            # 计算BOLL位置（避免除零错误）
            boll_range = second_last_row['boll_upper'] - second_last_row['boll_lower']
            if boll_range == 0:
                boll_position = 0.5  # 如果上下轨相等，设为中间位置
            else:
                boll_position = (second_last_row['close'] - second_last_row['boll_lower']) / boll_range

            # 判断是否满足异常条件
            # 条件1: 涨跌幅超过阈值 (已禁用)
            # price_change_exceeded = abs(price_change) >= self.price_change_threshold

            # 条件2: K线实体超过ATR倍数阈值
            atr_exceeded = body_atr_ratio >= self.atr_multiplier

            # 条件3: BOLL突破（接近上轨或下轨）
            boll_breakthrough = (boll_position >= self.boll_threshold or
                               boll_position <= (1 - self.boll_threshold))

            # 满足条件2或条件3即为异常K线（条件1已禁用）
            if atr_exceeded or boll_breakthrough:
                kline_info = {
                    'symbol': symbol,
                    'datetime': second_last_row.get('datetime', 'Second Last'),
                    'open': second_last_row['open'],
                    'high': second_last_row['high'],
                    'low': second_last_row['low'],
                    'close': second_last_row['close'],
                    'volume': second_last_row.get('volume', 0),
                    'price_change': price_change,
                    'candle_body': candle_body,
                    'atr': second_last_row['atr'],
                    'atr_tr': second_last_row.get('atr_tr', 0),
                    'body_atr_ratio': body_atr_ratio,
                    'boll_upper': second_last_row['boll_upper'],
                    'boll_middle': second_last_row['boll_middle'],
                    'boll_lower': second_last_row['boll_lower'],
                    'boll_position': boll_position,
                    'change_type': '上涨' if price_change > 0 else '下跌',
                    'trigger_reason': [],
                    'kline_data': df.copy()  # 保存完整的K线数据用于绘图
                }

                # 记录触发原因（只记录条件2和条件3）
                # 条件1已禁用，不再记录涨跌幅触发原因

                if atr_exceeded:
                    kline_info['trigger_reason'].append(f"K线实体是ATR的{body_atr_ratio:.2f}倍")

                if boll_breakthrough:
                    if boll_position >= self.boll_threshold:
                        kline_info['trigger_reason'].append(f"突破BOLL上轨(位置{boll_position:.2f})")
                    else:
                        kline_info['trigger_reason'].append(f"突破BOLL下轨(位置{boll_position:.2f})")

                logging.info(f"{symbol}: 倒数第二根K线异常 - {kline_info['change_type']} {price_change:+.2f}%, ATR比值: {body_atr_ratio:.2f}, BOLL位置: {boll_position:.2f}")
                logging.info(f"{symbol}: 触发原因: {'; '.join(kline_info['trigger_reason'])}")

                return [kline_info]
            else:
                logging.info(f"{symbol}: 倒数第二根K线正常 - {price_change:+.2f}%, ATR比值: {body_atr_ratio:.2f}, BOLL位置: {boll_position:.2f}")
                return []

        except Exception as e:
            logging.error(f"分析{symbol}倒数第二根K线时发生错误: {e}")
            return []

    def create_kline_chart(self, df, symbol, abnormal_index=-2):
        """
        创建K线图，包含BOLL带和异常K线标记

        参数:
        df: K线数据DataFrame
        symbol: 品种代码
        abnormal_index: 异常K线的索引位置（默认-2，倒数第二根）

        返回:
        str: base64编码的图片数据
        """
        try:
            # 设置中文字体
            plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False

            # 创建图形
            fig, ax = plt.subplots(figsize=(12, 8))

            # 只显示最近20根K线
            display_df = df.tail(20).copy()
            display_df.reset_index(drop=True, inplace=True)

            # 计算异常K线在显示数据中的位置
            if len(df) >= 20:
                abnormal_display_index = len(display_df) + abnormal_index  # -2变成18
            else:
                abnormal_display_index = len(df) + abnormal_index

            # 绘制K线
            for i, row in display_df.iterrows():
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']

                # K线颜色
                if close_price >= open_price:
                    color = 'red'  # 阳线
                    body_bottom = open_price
                    body_height = close_price - open_price
                else:
                    color = 'green'  # 阴线
                    body_bottom = close_price
                    body_height = open_price - close_price

                # 绘制影线
                ax.plot([i, i], [low_price, high_price], color='black', linewidth=1)

                # 绘制K线实体
                rect = Rectangle((i-0.3, body_bottom), 0.6, body_height,
                               facecolor=color, edgecolor='black', linewidth=1)
                ax.add_patch(rect)

                # 标记异常K线
                if i == abnormal_display_index:
                    # 添加黄色边框标记异常K线
                    highlight_rect = Rectangle((i-0.4, low_price-0.5), 0.8, high_price-low_price+1,
                                             facecolor='none', edgecolor='yellow', linewidth=3)
                    ax.add_patch(highlight_rect)

                    # 添加文字标记
                    ax.annotate('异常K线', xy=(i, high_price), xytext=(i, high_price+2),
                              ha='center', va='bottom', fontsize=10, color='red',
                              arrowprops=dict(arrowstyle='->', color='red'))

            # 绘制BOLL带
            if all(col in display_df.columns for col in ['boll_upper', 'boll_middle', 'boll_lower']):
                x_values = range(len(display_df))
                ax.plot(x_values, display_df['boll_upper'], 'b--', label='BOLL上轨', alpha=0.7)
                ax.plot(x_values, display_df['boll_middle'], 'b-', label='BOLL中轨', alpha=0.7)
                ax.plot(x_values, display_df['boll_lower'], 'b--', label='BOLL下轨', alpha=0.7)

                # 填充BOLL带区域
                ax.fill_between(x_values, display_df['boll_upper'], display_df['boll_lower'],
                              alpha=0.1, color='blue')

            # 设置图表属性
            ax.set_title(f'{symbol} K线图（最近20根，标记异常K线）', fontsize=14, fontweight='bold')
            ax.set_xlabel('K线序号', fontsize=12)
            ax.set_ylabel('价格', fontsize=12)
            ax.grid(True, alpha=0.3)
            ax.legend()

            # 设置x轴标签
            ax.set_xticks(range(0, len(display_df), max(1, len(display_df)//10)))

            # 调整布局
            plt.tight_layout()

            # 保存图片到内存
            img_buffer = io.BytesIO()
            plt.savefig(img_buffer, format='png', dpi=100, bbox_inches='tight')
            img_buffer.seek(0)

            # 转换为base64
            img_base64 = base64.b64encode(img_buffer.getvalue()).decode('utf-8')

            # 清理
            plt.close(fig)
            img_buffer.close()

            return img_base64

        except Exception as e:
            logging.error(f"创建{symbol}K线图时发生错误: {e}")
            plt.close('all')  # 确保清理所有图形
            return None

    def read_kline_files(self):
        """
        读取目标目录下的所有K线文件

        返回:
        dict: {symbol: DataFrame} 格式的数据字典
        """
        kline_data = {}

        if not os.path.exists(self.target_path):
            logging.error(f"目标目录不存在: {self.target_path}")
            return kline_data

        # 查找所有Excel文件
        excel_files = glob.glob(os.path.join(self.target_path, "*.xlsx"))

        if not excel_files:
            logging.warning(f"目录中没有找到Excel文件: {self.target_path}")
            return kline_data

        logging.info(f"找到 {len(excel_files)} 个Excel文件")

        for file_path in excel_files:
            try:
                # 从文件名提取品种代码
                file_name = os.path.basename(file_path)
                if file_name.startswith('~$'):  # 跳过临时文件
                    continue

                # 提取品种代码 (例如: DCE_v2509_kline_indicators.xlsx -> DCE.v2509)
                symbol = file_name.replace('_kline_indicators.xlsx', '').replace('_kline_data.xlsx', '').replace('_', '.')

                logging.info(f"正在读取文件: {file_name}")

                # 读取Excel文件
                df = pd.read_excel(file_path)

                if df.empty:
                    logging.warning(f"{symbol}: 文件为空")
                    continue

                # 检查必要的列
                required_columns = ['open', 'high', 'low', 'close']
                missing_columns = [col for col in required_columns if col not in df.columns]

                if missing_columns:
                    logging.warning(f"{symbol}: 缺少必要列: {missing_columns}")
                    continue

                # 数据清洗
                df = df.dropna(subset=required_columns)

                if len(df) == 0:
                    logging.warning(f"{symbol}: 清洗后数据为空")
                    continue

                # 检查是否包含指标列
                indicator_columns = ['atr', 'boll_upper', 'boll_middle', 'boll_lower']
                has_indicators = all(col in df.columns for col in indicator_columns)

                if has_indicators:
                    logging.info(f"{symbol}: 包含完整指标数据")
                else:
                    missing_indicators = [col for col in indicator_columns if col not in df.columns]
                    logging.warning(f"{symbol}: 缺少指标列: {missing_indicators}")
                    # 如果缺少指标，跳过该文件
                    continue

                kline_data[symbol] = df
                logging.info(f"{symbol}: 成功读取 {len(df)} 条K线数据")

            except Exception as e:
                logging.error(f"读取文件 {file_path} 时发生错误: {e}")
                continue

        logging.info(f"总共成功读取 {len(kline_data)} 个品种的数据")
        return kline_data

    def build_email_content(self, abnormal_klines):
        """
        构建邮件内容（包含K线图）

        参数:
        abnormal_klines: 异常K线列表

        返回:
        str: HTML格式的邮件内容
        """
        if not abnormal_klines:
            return ""

        current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        html_content = f"""
        <html>
        <head>
            <meta charset="utf-8">
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 15px; text-align: center; margin-bottom: 20px; }}
                .content {{ padding: 0; }}
                .highlight-up {{ color: green; font-weight: bold; }}
                .highlight-down {{ color: red; font-weight: bold; }}
                .summary {{ background-color: #e8f4fd; padding: 15px; margin-bottom: 20px; border-radius: 5px; }}
                .symbol-section {{ margin-bottom: 40px; border: 1px solid #ddd; border-radius: 5px; overflow: hidden; }}
                .symbol-header {{ background-color: #f8f9fa; padding: 15px; border-bottom: 1px solid #ddd; }}
                .symbol-content {{ padding: 20px; }}
                .kline-info {{ display: flex; flex-wrap: wrap; gap: 20px; margin-bottom: 20px; }}
                .info-item {{ flex: 1; min-width: 200px; }}
                .info-label {{ font-weight: bold; color: #666; }}
                .info-value {{ margin-top: 5px; }}
                .chart-container {{ text-align: center; margin: 20px 0; }}
                .chart-image {{ max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 5px; }}
                .trigger-reasons {{ background-color: #fff3cd; padding: 10px; border-radius: 5px; border-left: 4px solid #ffc107; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>倒数第二根K线异常波动提醒（ATR+BOLL指标）</h2>
            </div>
            <div class="content">
                <div class="summary">
                    <p><strong>检测到 {len(abnormal_klines)} 个品种的倒数第二根K线异常！</strong></p>
                    <p><strong>分析时间：</strong>{current_time}</p>
                    <p><strong>数据日期：</strong>{self.target_date}</p>
                    <p><strong>分析范围：</strong>仅倒数第二根K线</p>
                    <p><strong>触发条件：</strong>ATR倍数超标 或 BOLL带突破</p>
                </div>
        """

        # 为每个异常品种创建独立的展示区域
        for i, kline in enumerate(abnormal_klines, 1):
            highlight_class = "highlight-up" if kline['change_type'] == "上涨" else "highlight-down"

            # 格式化时间显示
            if kline['datetime'] != 'Second Last' and kline['datetime'] is not None:
                try:
                    if isinstance(kline['datetime'], str):
                        datetime_obj = pd.to_datetime(kline['datetime'])
                    else:
                        datetime_obj = kline['datetime']
                    datetime_str = datetime_obj.strftime('%Y-%m-%d %H:%M:%S')
                    time_str = datetime_obj.strftime('%H:%M:%S')
                except:
                    datetime_str = str(kline['datetime'])
                    time_str = "未知时间"
            else:
                datetime_str = "倒数第二根K线"
                time_str = "倒数第二根"

            trigger_reasons = "; ".join(kline['trigger_reason'])

            # 生成K线图
            chart_base64 = None
            if 'kline_data' in kline and kline['kline_data'] is not None:
                chart_base64 = self.create_kline_chart(kline['kline_data'], kline['symbol'])

            html_content += f"""
                <div class="symbol-section">
                    <div class="symbol-header">
                        <h3>#{i} {kline['symbol']} - <span class="{highlight_class}">{kline['change_type']} {kline['price_change']:+.2f}%</span></h3>
                    </div>
                    <div class="symbol-content">
                        <div class="kline-info">
                            <div class="info-item">
                                <div class="info-label">开盘时间</div>
                                <div class="info-value">{datetime_str}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">时间点</div>
                                <div class="info-value">{time_str}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">开盘价</div>
                                <div class="info-value">{kline['open']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">收盘价</div>
                                <div class="info-value">{kline['close']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最高价</div>
                                <div class="info-value">{kline['high']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最低价</div>
                                <div class="info-value">{kline['low']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">ATR值</div>
                                <div class="info-value">{kline['atr']:.4f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">实体/ATR比值</div>
                                <div class="info-value">{kline['body_atr_ratio']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">BOLL位置</div>
                                <div class="info-value">{kline['boll_position']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">BOLL上轨</div>
                                <div class="info-value">{kline['boll_upper']:.2f}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">BOLL下轨</div>
                                <div class="info-value">{kline['boll_lower']:.2f}</div>
                            </div>
                        </div>

                        <div class="trigger-reasons">
                            <strong>触发原因：</strong>{trigger_reasons}
                        </div>
            """

            # 添加K线图
            if chart_base64:
                html_content += f"""
                        <div class="chart-container">
                            <h4>K线图（最近20根，黄色标记为异常K线）</h4>
                            <img src="data:image/png;base64,{chart_base64}" class="chart-image" alt="{kline['symbol']} K线图">
                        </div>
                """
            else:
                html_content += """
                        <div class="chart-container">
                            <p style="color: #666; font-style: italic;">K线图生成失败</p>
                        </div>
                """

            html_content += """
                    </div>
                </div>
            """

        html_content += f"""
                <div class="summary">
                    <h3>分析参数说明</h3>
                    <p><strong>ATR周期：</strong>{self.atr_period} 根K线</p>
                    <p><strong>ATR倍数阈值：</strong>{self.atr_multiplier} (K线实体大小超过ATR的{self.atr_multiplier}倍时触发)</p>
                    <p><strong>BOLL突破阈值：</strong>{self.boll_threshold} (价格位置超过{self.boll_threshold*100:.0f}%或低于{(1-self.boll_threshold)*100:.0f}%时触发)</p>
                    <p><strong>注意：</strong>涨跌幅条件已禁用，仅使用ATR和BOLL指标判断异常</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html_content

    def send_alert_email(self, abnormal_klines):
        """
        发送异常K线提醒邮件

        参数:
        abnormal_klines: 异常K线列表

        返回:
        bool: 是否发送成功
        """
        if not abnormal_klines:
            logging.info("没有异常K线，不发送邮件")
            return True

        try:
            # 构建邮件内容
            email_content = self.build_email_content(abnormal_klines)

            # 构建邮件主题
            up_count = sum(1 for k in abnormal_klines if k['change_type'] == '上涨')
            down_count = sum(1 for k in abnormal_klines if k['change_type'] == '下跌')
            boll_count = sum(1 for k in abnormal_klines if any('BOLL' in reason for reason in k['trigger_reason']))
            atr_count = sum(1 for k in abnormal_klines if any('ATR' in reason for reason in k['trigger_reason']))

            subject = f"倒数第二根K线异常提醒(ATR+BOLL) - {self.target_date} - {len(abnormal_klines)}个品种异常(上涨{up_count}个,下跌{down_count}个,ATR超标{atr_count}个,BOLL突破{boll_count}个)"

            # 发送邮件
            send_mail(
                mail_content=email_content,
                subject=subject,
                receives=['<EMAIL>']  # 可以根据需要修改接收邮箱
            )

            logging.info(f"异常K线提醒邮件发送成功，共 {len(abnormal_klines)} 个异常K线")
            return True

        except Exception as e:
            logging.error(f"发送邮件时发生错误: {e}")
            return False

    def analyze_all_symbols(self):
        """
        分析所有品种的K线数据

        返回:
        list: 所有异常K线的列表
        """
        logging.info("开始分析所有品种的K线数据...")

        # 读取K线数据
        kline_data = self.read_kline_files()

        if not kline_data:
            logging.warning("没有读取到任何K线数据")
            return []

        all_abnormal_klines = []

        # 分析每个品种的倒数第二根K线
        for symbol, df in kline_data.items():
            logging.info(f"正在分析品种: {symbol} (倒数第二根K线)")

            abnormal_klines = self.analyze_second_last_kline(df, symbol)

            if abnormal_klines:
                all_abnormal_klines.extend(abnormal_klines)
                logging.info(f"{symbol}: 倒数第二根K线异常")
            else:
                logging.info(f"{symbol}: 倒数第二根K线正常")

        logging.info(f"分析完成，总共发现 {len(all_abnormal_klines)} 个异常K线")

        return all_abnormal_klines

    def run_analysis(self, send_email=True):
        """
        运行完整的分析流程

        参数:
        send_email: 是否发送邮件

        返回:
        dict: 分析结果
        """
        try:
            logging.info("=" * 50)
            logging.info("开始倒数第二根K线异常波动分析（ATR+BOLL指标）")
            logging.info("=" * 50)

            # 分析所有品种
            abnormal_klines = self.analyze_all_symbols()

            # 统计结果
            result = {
                'total_abnormal': len(abnormal_klines),
                'up_count': sum(1 for k in abnormal_klines if k['change_type'] == '上涨'),
                'down_count': sum(1 for k in abnormal_klines if k['change_type'] == '下跌'),
                'boll_breakthrough_count': sum(1 for k in abnormal_klines if any('BOLL' in reason for reason in k['trigger_reason'])),
                'atr_exceeded_count': sum(1 for k in abnormal_klines if any('ATR' in reason for reason in k['trigger_reason'])),
                'price_change_count': sum(1 for k in abnormal_klines if any('涨跌幅' in reason for reason in k['trigger_reason'])),
                'symbols': list(set(k['symbol'] for k in abnormal_klines)),
                'abnormal_klines': abnormal_klines
            }

            # 输出统计信息
            logging.info("=" * 30)
            logging.info("倒数第二根K线分析结果统计（仅ATR+BOLL条件）")
            logging.info("=" * 30)
            logging.info(f"异常品种总数: {result['total_abnormal']}")
            logging.info(f"上涨异常: {result['up_count']} 个品种")
            logging.info(f"下跌异常: {result['down_count']} 个品种")
            logging.info(f"ATR超标: {result['atr_exceeded_count']} 个品种")
            logging.info(f"BOLL突破: {result['boll_breakthrough_count']} 个品种")
            logging.info(f"涉及品种: {len(result['symbols'])} 个")
            logging.info("注意: 涨跌幅条件已禁用")

            if result['symbols']:
                logging.info(f"异常品种列表: {', '.join(result['symbols'])}")

            # 发送邮件
            if send_email and abnormal_klines:
                email_sent = self.send_alert_email(abnormal_klines)
                result['email_sent'] = email_sent
            else:
                result['email_sent'] = False
                if not abnormal_klines:
                    logging.info("没有异常K线，不发送邮件")
                else:
                    logging.info("邮件发送已禁用")

            logging.info("=" * 50)
            logging.info("倒数第二根K线异常波动分析完成")
            logging.info("=" * 50)

            return result

        except Exception as e:
            logging.error(f"运行分析时发生错误: {e}")
            return {
                'total_abnormal': 0,
                'up_count': 0,
                'down_count': 0,
                'boll_breakthrough_count': 0,
                'atr_exceeded_count': 0,
                'price_change_count': 0,
                'symbols': [],
                'abnormal_klines': [],
                'email_sent': False,
                'error': str(e)
            }

def main():
    """
    主函数
    """
    print("=" * 60)
    print("倒数第二根K线异常波动分析器（仅ATR+BOLL指标）")
    print("=" * 60)

    # 检查命令行参数
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()

        if command == "help" or command == "-h":
            print("使用方法:")
            print("  python b1.py                    - 分析当日倒数第二根K线并发送邮件")
            print("  python b1.py [日期]             - 分析指定日期的倒数第二根K线")
            print("  python b1.py [日期] --no-email  - 分析但不发送邮件")
            print("  python b1.py test               - 测试模式（不发送邮件）")
            print("  python b1.py help               - 显示帮助信息")
            print("")
            print("日期格式: YYYYMMDD (例如: 20250109)")
            print("默认使用当日日期")
            print("注意: 只分析每个品种的倒数第二根K线")
            print("触发条件: 仅使用ATR和BOLL指标（涨跌幅条件已禁用）")
            return

        elif command == "test":
            print("测试模式：分析倒数第二根K线但不发送邮件")
            analyzer = KLineAnalyzer()  # 使用当日日期
            result = analyzer.run_analysis(send_email=False)
            print(f"测试完成，发现 {result['total_abnormal']} 个品种的倒数第二根K线异常")
            return

    # 解析参数
    target_date = None  # 默认使用当日
    send_email = True

    if len(sys.argv) > 1:
        # 检查是否是日期格式
        date_arg = sys.argv[1]
        if date_arg.isdigit() and len(date_arg) == 8:
            target_date = date_arg
            print(f"使用指定日期: {target_date}")

        # 检查是否禁用邮件
        if "--no-email" in sys.argv:
            send_email = False
            print("邮件发送已禁用")

    try:
        # 创建分析器
        analyzer = KLineAnalyzer(target_date=target_date)

        # 运行分析
        result = analyzer.run_analysis(send_email=send_email)

        # 输出最终结果
        print("\n" + "=" * 40)
        print("最终结果")
        print("=" * 40)
        print(f"分析日期: {analyzer.target_date}")
        print(f"分析范围: 仅倒数第二根K线")
        print(f"触发条件: 仅ATR+BOLL（涨跌幅条件已禁用）")
        print(f"异常品种总数: {result['total_abnormal']}")
        print(f"上涨异常: {result['up_count']} 个品种")
        print(f"下跌异常: {result['down_count']} 个品种")
        print(f"ATR超标: {result['atr_exceeded_count']} 个品种")
        print(f"BOLL突破: {result['boll_breakthrough_count']} 个品种")
        print(f"涉及品种数: {len(result['symbols'])}")

        if result.get('email_sent'):
            print("✓ 邮件发送成功")
        elif result['total_abnormal'] > 0:
            print("✗ 邮件发送失败或已禁用")
        else:
            print("- 无需发送邮件")

        if 'error' in result:
            print(f"错误: {result['error']}")

        print("=" * 40)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        logging.error(f"程序运行时发生错误: {e}")
        print(f"程序运行失败: {e}")

if __name__ == "__main__":
    main()